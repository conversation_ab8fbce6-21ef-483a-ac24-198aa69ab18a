"""Tests for the main API endpoint."""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch

from app.main import app
from app.models import GeneratedQuestion, ValidationInfo, QuestionTags, QuestionOption, ScoringKey


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
def sample_request():
    """Sample request data."""
    return {
        "theory": "Big Five",
        "construct": "Agreeableness",
        "target_age_group": "14-16",
        "prompt_context": "Create a question about collaborating on a difficult school project."
    }


@pytest.fixture
def sample_generated_question():
    """Sample generated question."""
    return GeneratedQuestion(
        question_id="qid_test123",
        question_text="How do you handle team conflicts?",
        options=[
            QuestionOption(
                option_id="A",
                option_text="Listen to all perspectives",
                scoring_key=ScoringKey(trait="Agreeableness", weight=2)
            ),
            QuestionOption(
                option_id="B",
                option_text="Avoid the conflict",
                scoring_key=ScoringKey(trait="Agreeableness", weight=0)
            )
        ],
        tags=QuestionTags(
            theory="Big Five",
            construct="Agreeableness",
            age_group="14-16"
        ),
        validation_info=ValidationInfo(
            verdict="PASS",
            confidence_score=0.95,
            justification="Question effectively measures the construct"
        )
    )


class TestAPI:
    """Test cases for the API endpoints."""
    
    def test_root_endpoint(self, client):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "service" in data
        assert "version" in data
        assert "endpoints" in data
    
    @patch('app.services.question_service.generate_and_validate_question')
    @patch('app.database.chroma_client.get_collection_info')
    def test_health_endpoint(self, mock_collection_info, mock_generate, client):
        """Test the health check endpoint."""
        mock_collection_info.return_value = {
            "name": "psychometrics_kb",
            "document_count": 100
        }
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "services" in data
        assert "knowledge_base" in data
    
    @patch('app.services.question_service.generate_and_validate_question')
    def test_generate_question_success(self, mock_generate, client, sample_request, sample_generated_question):
        """Test successful question generation."""
        mock_generate.return_value = sample_generated_question
        
        response = client.post("/v1/questions/generate_and_validate", json=sample_request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["question_id"] == "qid_test123"
        assert data["question_text"] == "How do you handle team conflicts?"
        assert len(data["options"]) == 2
        assert data["validation_info"]["verdict"] == "PASS"
    
    def test_generate_question_invalid_request(self, client):
        """Test question generation with invalid request."""
        invalid_request = {
            "theory": "Big Five",
            # Missing required fields
        }
        
        response = client.post("/v1/questions/generate_and_validate", json=invalid_request)
        assert response.status_code == 422  # Validation error
    
    @patch('app.services.question_service.generate_and_validate_question')
    def test_generate_question_validation_failure(self, mock_generate, client, sample_request):
        """Test question generation with validation failure."""
        from app.models import ValidationFailureResponse, ValidationInfo
        
        mock_generate.return_value = ValidationFailureResponse(
            validation_info=ValidationInfo(
                verdict="FAIL",
                confidence_score=0.75,
                justification="Question does not meet quality standards"
            ),
            question_id="qid_failed123"
        )
        
        response = client.post("/v1/questions/generate_and_validate", json=sample_request)
        assert response.status_code == 422
        
        data = response.json()
        assert "detail" in data
        assert data["detail"]["validation_info"]["verdict"] == "FAIL"


if __name__ == "__main__":
    pytest.main([__file__])
