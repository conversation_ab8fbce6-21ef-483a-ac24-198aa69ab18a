#!/usr/bin/env python3
"""<PERSON><PERSON><PERSON> to populate ChromaDB with psychometric knowledge from PDF files."""

import os
import sys
import logging
from pathlib import Path
import uuid
from typing import List, Dict, Any

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.database import chroma_client
from app.config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeBasePopulator:
    """Populates the ChromaDB knowledge base with psychometric content."""
    
    def __init__(self):
        self.sample_chunks = [
            {
                "content": "Agreeableness is one of the Big Five personality traits. It reflects individual differences in cooperation and social harmony. People high in agreeableness tend to be trusting, helpful, and sympathetic. They are more likely to compromise during conflicts and show concern for others' well-being.",
                "metadata": {"source": "Big Five Theory", "construct": "Agreeableness", "page": 1}
            },
            {
                "content": "In psychometric testing, agreeableness is typically measured through items that assess tendencies toward altruism, trust, modesty, and compliance. High scorers are characterized by their cooperative nature and desire to maintain harmonious relationships.",
                "metadata": {"source": "Psychometric Handbook", "construct": "Agreeableness", "page": 45}
            },
            {
                "content": "Conscientiousness reflects individual differences in organization, persistence, and motivation in goal-directed behavior. Highly conscientious individuals are organized, responsible, and dependable. They tend to be self-disciplined and achievement-oriented.",
                "metadata": {"source": "Big Five Theory", "construct": "Conscientiousness", "page": 2}
            },
            {
                "content": "Extraversion is characterized by positive emotions, surgency, assertiveness, sociability and the tendency to seek stimulation in the company of others. Extraverts are energetic and tend to experience positive emotions more frequently.",
                "metadata": {"source": "Personality Psychology", "construct": "Extraversion", "page": 78}
            },
            {
                "content": "Neuroticism refers to the tendency to experience negative emotions such as anxiety, depression, and vulnerability to stress. Individuals high in neuroticism are more likely to interpret ordinary situations as threatening and minor frustrations as hopelessly difficult.",
                "metadata": {"source": "Clinical Psychology", "construct": "Neuroticism", "page": 123}
            },
            {
                "content": "Openness to Experience reflects the degree of intellectual curiosity, creativity and a preference for novelty and variety. Open individuals are imaginative, curious about the inner and outer worlds, and willing to entertain novel ideas and unconventional values.",
                "metadata": {"source": "Cognitive Psychology", "construct": "Openness", "page": 67}
            },
            {
                "content": "When designing psychometric items for adolescents (ages 14-16), it is crucial to use age-appropriate language and scenarios. Questions should relate to school, peer relationships, and family dynamics that are relevant to this developmental stage.",
                "metadata": {"source": "Developmental Psychology", "construct": "Age-Appropriate Testing", "page": 234}
            },
            {
                "content": "Effective psychometric items should have clear, unambiguous wording and avoid double-barreled questions. Each response option should represent a different level of the trait being measured, with weights typically ranging from -3 to +3.",
                "metadata": {"source": "Test Construction Manual", "construct": "Item Development", "page": 89}
            },
            {
                "content": "Validation of psychometric instruments requires evidence of content validity, construct validity, and criterion validity. Items should be reviewed by subject matter experts and tested with representative samples.",
                "metadata": {"source": "Psychometric Standards", "construct": "Validation", "page": 156}
            },
            {
                "content": "In team-based scenarios, agreeable individuals are more likely to prioritize group harmony over personal preferences. They tend to be cooperative, avoid conflicts, and show empathy toward team members' perspectives.",
                "metadata": {"source": "Social Psychology", "construct": "Agreeableness", "page": 201}
            }
        ]
    
    async def populate_knowledge_base(self):
        """Populate the ChromaDB collection with sample psychometric knowledge."""
        try:
            # Initialize ChromaDB client
            await chroma_client.initialize()
            logger.info("ChromaDB client initialized")
            
            # Check current collection status
            collection_info = await chroma_client.get_collection_info()
            logger.info(f"Current collection info: {collection_info}")
            
            if collection_info["document_count"] > 0:
                logger.info(f"Collection already contains {collection_info['document_count']} documents")
                response = input("Do you want to add more documents? (y/n): ")
                if response.lower() != 'y':
                    return
            
            # Prepare documents for insertion
            documents = [chunk["content"] for chunk in self.sample_chunks]
            metadatas = [chunk["metadata"] for chunk in self.sample_chunks]
            ids = [f"doc_{uuid.uuid4().hex[:8]}" for _ in self.sample_chunks]
            
            # Add documents to the collection
            logger.info(f"Adding {len(documents)} documents to the knowledge base...")
            await chroma_client.add_documents(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            # Verify the addition
            updated_info = await chroma_client.get_collection_info()
            logger.info(f"Knowledge base populated successfully!")
            logger.info(f"Total documents: {updated_info['document_count']}")
            
            # Test a sample query
            logger.info("Testing knowledge base with sample query...")
            results = await chroma_client.search_knowledge_base("Agreeableness", n_results=3)
            
            logger.info(f"Sample query returned {len(results)} results:")
            for i, result in enumerate(results, 1):
                logger.info(f"Result {i}: {result['content'][:100]}...")
            
        except Exception as e:
            logger.error(f"Error populating knowledge base: {e}")
            raise


async def main():
    """Main function to populate the knowledge base."""
    try:
        # Check if Google API key is set
        if not settings.google_api_key:
            logger.error("GOOGLE_API_KEY environment variable is required")
            sys.exit(1)
        
        populator = KnowledgeBasePopulator()
        await populator.populate_knowledge_base()
        
        logger.info("Knowledge base population completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
    except Exception as e:
        logger.error(f"Failed to populate knowledge base: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
