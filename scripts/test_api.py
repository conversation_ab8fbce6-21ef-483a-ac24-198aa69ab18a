#!/usr/bin/env python3
"""Script to test the Psychometric Question Generator API."""

import asyncio
import json
import logging
import httpx
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class APITester:
    """Test the Psychometric Question Generator API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_health_endpoint(self) -> bool:
        """Test the health check endpoint."""
        try:
            logger.info("Testing health endpoint...")
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Health check passed: {data['status']}")
                logger.info(f"Knowledge base: {data.get('knowledge_base', {})}")
                return True
            else:
                logger.error(f"Health check failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing health endpoint: {e}")
            return False
    
    async def test_question_generation(self) -> bool:
        """Test the question generation endpoint."""
        try:
            logger.info("Testing question generation endpoint...")
            
            # Sample request
            request_data = {
                "theory": "Big Five",
                "construct": "Agreeableness",
                "target_age_group": "14-16",
                "prompt_context": "Create a question about collaborating on a difficult school project."
            }
            
            logger.info(f"Sending request: {json.dumps(request_data, indent=2)}")
            
            response = await self.client.post(
                f"{self.base_url}/v1/questions/generate_and_validate",
                json=request_data
            )
            
            logger.info(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info("✅ Question generation successful!")
                logger.info(f"Question ID: {data['question_id']}")
                logger.info(f"Question: {data['question_text']}")
                logger.info(f"Options: {len(data['options'])}")
                logger.info(f"Validation: {data['validation_info']['verdict']} (confidence: {data['validation_info']['confidence_score']})")
                
                # Pretty print the full response
                print("\n" + "="*50)
                print("GENERATED QUESTION:")
                print("="*50)
                print(json.dumps(data, indent=2, default=str))
                print("="*50)
                
                return True
                
            elif response.status_code == 422:
                data = response.json()
                logger.warning("❌ Question validation failed")
                logger.warning(f"Reason: {data['detail']['validation_info']['justification']}")
                return False
                
            else:
                logger.error(f"❌ Request failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing question generation: {e}")
            return False
    
    async def test_multiple_constructs(self) -> None:
        """Test question generation for multiple constructs."""
        constructs = [
            ("Big Five", "Conscientiousness", "Create a question about completing homework assignments."),
            ("Big Five", "Extraversion", "Create a question about participating in class discussions."),
            ("Big Five", "Neuroticism", "Create a question about handling test anxiety."),
            ("Big Five", "Openness", "Create a question about trying new learning methods.")
        ]
        
        logger.info(f"Testing {len(constructs)} different constructs...")
        
        for theory, construct, context in constructs:
            logger.info(f"\nTesting {construct}...")
            
            request_data = {
                "theory": theory,
                "construct": construct,
                "target_age_group": "14-16",
                "prompt_context": context
            }
            
            try:
                response = await self.client.post(
                    f"{self.base_url}/v1/questions/generate_and_validate",
                    json=request_data
                )
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"✅ {construct}: {data['validation_info']['verdict']} (confidence: {data['validation_info']['confidence_score']})")
                elif response.status_code == 422:
                    data = response.json()
                    logger.warning(f"❌ {construct}: Validation failed - {data['detail']['validation_info']['justification']}")
                else:
                    logger.error(f"❌ {construct}: Request failed - {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ {construct}: Error - {e}")
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main test function."""
    tester = APITester()
    
    try:
        logger.info("Starting API tests...")
        
        # Test health endpoint
        health_ok = await tester.test_health_endpoint()
        if not health_ok:
            logger.error("Health check failed. Make sure the API is running and ChromaDB is available.")
            return
        
        # Test single question generation
        generation_ok = await tester.test_question_generation()
        if not generation_ok:
            logger.error("Question generation test failed.")
            return
        
        # Test multiple constructs
        await tester.test_multiple_constructs()
        
        logger.info("All tests completed!")
        
    except KeyboardInterrupt:
        logger.info("Tests cancelled by user")
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
