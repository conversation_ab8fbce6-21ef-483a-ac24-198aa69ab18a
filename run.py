#!/usr/bin/env python3
"""Startup script for the Psychometric Question Generator API."""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from app.config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


def main():
    """Main entry point for the application."""
    try:
        # Validate required environment variables
        if not settings.google_api_key:
            logger.error("GOOGLE_API_KEY environment variable is required")
            sys.exit(1)
        
        logger.info(f"Starting {settings.app_name} v{settings.app_version}")
        logger.info(f"Server will run on {settings.host}:{settings.port}")
        logger.info(f"Debug mode: {settings.debug}")
        
        # Start the server
        uvicorn.run(
            "app.main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
