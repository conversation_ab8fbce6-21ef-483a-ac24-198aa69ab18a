"""ChromaDB client and vector database operations."""

import logging
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings as ChromaSettings
from chromadb.utils import embedding_functions

from app.config import settings

logger = logging.getLogger(__name__)


class ChromaDBClient:
    """ChromaDB client for vector database operations."""
    
    def __init__(self):
        """Initialize ChromaDB client and collection."""
        self._client: Optional[chromadb.Client] = None
        self._collection: Optional[chromadb.Collection] = None
        self._embedding_function = None
        
    async def initialize(self) -> None:
        """Initialize the ChromaDB client and collection."""
        try:
            # Initialize ChromaDB client
            self._client = chromadb.Client(
                Settings=ChromaSettings(
                    chroma_server_host=settings.chromadb_host,
                    chroma_server_http_port=settings.chromadb_port,
                    anonymized_telemetry=False
                )
            )
            
            # Set up embedding function (using Google's text-embedding-004)
            self._embedding_function = embedding_functions.GoogleGenerativeAiEmbeddingFunction(
                api_key=settings.google_api_key,
                model_name=settings.embedding_model
            )
            
            # Get or create the psychometrics knowledge base collection
            try:
                self._collection = self._client.get_collection(
                    name=settings.chromadb_collection_name,
                    embedding_function=self._embedding_function
                )
                logger.info(f"Connected to existing collection: {settings.chromadb_collection_name}")
            except Exception:
                # Collection doesn't exist, create it
                self._collection = self._client.create_collection(
                    name=settings.chromadb_collection_name,
                    embedding_function=self._embedding_function,
                    metadata={"description": "Psychometric textbook knowledge base"}
                )
                logger.info(f"Created new collection: {settings.chromadb_collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB client: {e}")
            raise
    
    async def search_knowledge_base(
        self, 
        query: str, 
        n_results: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Search the psychometrics knowledge base for relevant chunks.
        
        Args:
            query: Search query (typically the construct name)
            n_results: Number of results to return
            
        Returns:
            List of relevant document chunks with metadata
        """
        if not self._collection:
            raise RuntimeError("ChromaDB client not initialized")
            
        try:
            results = self._collection.query(
                query_texts=[query],
                n_results=n_results,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results for easier consumption
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    formatted_results.append({
                        "content": doc,
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "distance": results["distances"][0][i] if results["distances"] else 0.0
                    })
            
            logger.info(f"Retrieved {len(formatted_results)} chunks for query: {query}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            raise
    
    async def add_documents(
        self, 
        documents: List[str], 
        metadatas: List[Dict[str, Any]], 
        ids: List[str]
    ) -> None:
        """
        Add documents to the knowledge base collection.
        
        Args:
            documents: List of document texts
            metadatas: List of metadata dictionaries
            ids: List of unique document IDs
        """
        if not self._collection:
            raise RuntimeError("ChromaDB client not initialized")
            
        try:
            self._collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            logger.info(f"Added {len(documents)} documents to collection")
            
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            raise
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the current collection."""
        if not self._collection:
            raise RuntimeError("ChromaDB client not initialized")
            
        try:
            count = self._collection.count()
            return {
                "name": settings.chromadb_collection_name,
                "document_count": count,
                "embedding_function": settings.embedding_model
            }
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            raise


# Global ChromaDB client instance
chroma_client = ChromaDBClient()
