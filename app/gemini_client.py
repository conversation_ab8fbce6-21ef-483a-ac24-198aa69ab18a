"""Google Gemini API client for question generation and validation."""

import json
import logging
from typing import Dict, Any, Optional
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from app.config import settings
from app.models import GeneratedQuestionRaw, ValidationInfo

logger = logging.getLogger(__name__)


class GeminiClient:
    """Google Gemini API client for LLM operations."""
    
    def __init__(self):
        """Initialize Gemini client."""
        self._model: Optional[genai.GenerativeModel] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize the Gemini client with API key and model."""
        try:
            # Configure the API key
            genai.configure(api_key=settings.google_api_key)
            
            # Initialize the model with safety settings
            self._model = genai.GenerativeModel(
                model_name=settings.gemini_model,
                safety_settings={
                    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
                }
            )
            
            self._initialized = True
            logger.info(f"Gemini client initialized with model: {settings.gemini_model}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    
    async def generate_question(
        self, 
        theory: str, 
        construct: str, 
        target_age_group: str, 
        prompt_context: str
    ) -> GeneratedQuestionRaw:
        """
        Generate a psychometric question using the Generator Prompt Template.
        
        Args:
            theory: Psychometric theory
            construct: Specific construct to measure
            target_age_group: Target age group
            prompt_context: Context for question creation
            
        Returns:
            Generated question object
        """
        if not self._initialized or not self._model:
            raise RuntimeError("Gemini client not initialized")
        
        # Generator Prompt Template
        prompt = f"""You are a world-class psychometrician designing a test item. Your task is to create a single, complete question object based on the user's request.

**Request:**
- Theory: {theory}
- Construct: {construct}  
- Target Age: {target_age_group}
- Context: {prompt_context}

**Instructions:**
1. Create a `question_text` that is clear, unbiased, and age-appropriate.
2. Create an array of 2-4 `options` with unique option_id values (A, B, C, D).
3. For EACH option, create a `scoring_key` object containing:
   - `"trait"`: The exact construct name (e.g., "{construct}")
   - `"weight"`: Integer score (-3 to +3) representing trait intensity for this option
4. You MUST respond ONLY with a single, minified JSON object containing `question_text` and `options` array. No additional keys or text."""
        
        try:
            response = self._model.generate_content(prompt)
            
            if not response.text:
                raise ValueError("Empty response from Gemini API")
            
            # Parse the JSON response
            try:
                question_data = json.loads(response.text.strip())
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {response.text}")
                raise ValueError(f"Invalid JSON response from Gemini: {e}")
            
            # Validate and create the GeneratedQuestionRaw object
            generated_question = GeneratedQuestionRaw(**question_data)
            
            logger.info(f"Successfully generated question for construct: {construct}")
            return generated_question
            
        except Exception as e:
            logger.error(f"Error generating question: {e}")
            raise
    
    async def validate_question(
        self, 
        enriched_question_json: str, 
        retrieved_chunks: str
    ) -> ValidationInfo:
        """
        Validate a question using the Validator Prompt Template.
        
        Args:
            enriched_question_json: JSON string of the enriched question
            retrieved_chunks: Retrieved knowledge base chunks
            
        Returns:
            Validation information
        """
        if not self._initialized or not self._model:
            raise RuntimeError("Gemini client not initialized")
        
        # Validator Prompt Template
        prompt = f"""You are a meticulous Quality Assurance specialist and PhD-level psychometrician from the University of Oxford. Your task is to validate a psychometric question object using the provided expert knowledge from established textbooks.

**Expert Knowledge (Context from Textbooks):**
---
{retrieved_chunks}
---

**Question Object to Validate:**
---
{enriched_question_json}
---

**Validation Criteria:**
Based ONLY on the provided expert knowledge and psychometric best practices, evaluate:
1. `question_text`: Is it clear, unbiased, and age-appropriate?
2. `options`: Are they distinct, clear, and cover the trait spectrum?
3. `scoring_key`: Do the assigned weights logically correspond to the trait definition? Is the scoring theoretically sound?

You MUST respond ONLY with a single, minified JSON object:
{{"verdict": "PASS" or "FAIL", "confidence_score": 0.0-1.0, "justification": "Brief explanation referencing scoring logic and theoretical alignment from the expert knowledge."}}"""
        
        try:
            response = self._model.generate_content(prompt)
            
            if not response.text:
                raise ValueError("Empty response from Gemini API")
            
            # Parse the JSON response
            try:
                validation_data = json.loads(response.text.strip())
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse validation JSON response: {response.text}")
                raise ValueError(f"Invalid JSON response from Gemini: {e}")
            
            # Create ValidationInfo object
            validation_info = ValidationInfo(**validation_data)
            
            logger.info(f"Question validation completed: {validation_info.verdict} (confidence: {validation_info.confidence_score})")
            return validation_info
            
        except Exception as e:
            logger.error(f"Error validating question: {e}")
            raise


# Global Gemini client instance
gemini_client = GeminiClient()
