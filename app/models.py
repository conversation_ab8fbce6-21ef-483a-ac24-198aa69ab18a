"""Pydantic models for request/response schemas."""

from datetime import datetime
from typing import List, Literal
from pydantic import BaseModel, Field


class QuestionGenerationRequest(BaseModel):
    """Input schema for question generation request."""
    theory: str = Field(..., description="Psychometric theory (e.g., 'Big Five')")
    construct: str = Field(..., description="Specific construct to measure (e.g., 'Agreeableness')")
    target_age_group: str = Field(..., description="Target age group (e.g., '14-16')")
    prompt_context: str = Field(..., description="Context for question creation")


class ScoringKey(BaseModel):
    """Scoring key for each option."""
    trait: str = Field(..., description="The trait being measured")
    weight: int = Field(..., ge=-3, le=3, description="Score weight from -3 to +3")


class QuestionOption(BaseModel):
    """Individual question option with scoring."""
    option_id: str = Field(..., description="Unique option identifier (A, B, C, D)")
    option_text: str = Field(..., description="The option text")
    scoring_key: ScoringKey = Field(..., description="Scoring information for this option")


class QuestionTags(BaseModel):
    """Metadata tags for the question."""
    theory: str = Field(..., description="Psychometric theory")
    construct: str = Field(..., description="Construct being measured")
    age_group: str = Field(..., description="Target age group")


class ValidationInfo(BaseModel):
    """Validation information from RAG-based verification."""
    verdict: Literal["PASS", "FAIL"] = Field(..., description="Validation verdict")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score 0.0-1.0")
    justification: str = Field(..., description="Brief explanation of validation decision")


class GeneratedQuestion(BaseModel):
    """Complete generated and validated question."""
    question_id: str = Field(..., description="Unique question identifier")
    question_text: str = Field(..., description="The question text")
    options: List[QuestionOption] = Field(..., min_items=2, max_items=4, description="Question options")
    tags: QuestionTags = Field(..., description="Question metadata")
    validation_info: ValidationInfo = Field(..., description="Validation results")
    version: float = Field(default=1.0, description="Question version")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class ValidationFailureResponse(BaseModel):
    """Response for validation failures."""
    error: str = Field(default="Validation failed", description="Error message")
    validation_info: ValidationInfo = Field(..., description="Validation failure details")
    question_id: str = Field(..., description="Question ID that failed validation")


# Internal models for generation pipeline
class GeneratedQuestionRaw(BaseModel):
    """Raw question from generation step (before enrichment)."""
    question_text: str
    options: List[QuestionOption]


class ValidationRequest(BaseModel):
    """Internal model for validation request."""
    enriched_question_json: str
    retrieved_chunks: str
