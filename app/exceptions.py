"""Custom exceptions for the Psychometric Question Generator API."""

from typing import Optional, Dict, Any


class PsychometricAPIException(Exception):
    """Base exception for all API-related errors."""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = 500, 
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class QuestionGenerationError(PsychometricAPIException):
    """Exception raised when question generation fails."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)


class ValidationError(PsychometricAPIException):
    """Exception raised when question validation fails."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=422, details=details)


class DatabaseConnectionError(PsychometricAPIException):
    """Exception raised when database connection fails."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=503, details=details)


class GeminiAPIError(PsychometricAPIException):
    """Exception raised when Gemini API calls fail."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=502, details=details)


class ConfigurationError(PsychometricAPIException):
    """Exception raised when configuration is invalid."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)
