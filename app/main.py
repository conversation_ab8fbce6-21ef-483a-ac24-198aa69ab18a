"""Main FastAPI application for Psychometric Question Generator."""

import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON>AP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.config import settings
from app.database import chroma_client
from app.gemini_client import gemini_client
from app.models import (
    QuestionGenerationRequest, 
    GeneratedQuestion, 
    ValidationFailureResponse
)
from app.services import question_service

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting Psychometric Question Generator API")
    try:
        # Initialize ChromaDB client
        await chroma_client.initialize()
        logger.info("ChromaDB client initialized")
        
        # Initialize Gemini client
        await gemini_client.initialize()
        logger.info("Gemini client initialized")
        
        # Log collection info
        collection_info = await chroma_client.get_collection_info()
        logger.info(f"Knowledge base ready: {collection_info}")
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Psychometric Question Generator API")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Robust Python FastAPI service for psychometric question generation with RAG-powered validation",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": "An unexpected error occurred. Please try again later."
        }
    )


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "healthy",
        "endpoints": {
            "generate_and_validate": "/v1/questions/generate_and_validate"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check ChromaDB connection
        collection_info = await chroma_client.get_collection_info()
        
        return {
            "status": "healthy",
            "services": {
                "chromadb": "connected",
                "gemini": "initialized"
            },
            "knowledge_base": collection_info
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail="Service unavailable - health check failed"
        )


@app.post(
    "/v1/questions/generate_and_validate",
    response_model=GeneratedQuestion,
    responses={
        200: {"model": GeneratedQuestion, "description": "Successfully generated and validated question"},
        400: {"description": "Invalid request body"},
        422: {"model": ValidationFailureResponse, "description": "Question validation failed"},
        500: {"description": "Internal server error"}
    }
)
async def generate_and_validate_question(request: QuestionGenerationRequest):
    """
    Generate and validate a psychometric question.
    
    This endpoint executes the complete workflow:
    1. Input validation
    2. Question generation using Gemini API
    3. Metadata enrichment
    4. RAG-based validation against expert knowledge
    5. Approval/rejection decision
    
    Returns either an approved question or validation failure details.
    """
    try:
        logger.info(f"Received question generation request for construct: {request.construct}")
        
        # Execute the complete generation and validation workflow
        result = await question_service.generate_and_validate_question(request)
        
        # Check if validation failed
        if isinstance(result, ValidationFailureResponse):
            logger.warning(f"Question validation failed: {result.validation_info.justification}")
            raise HTTPException(
                status_code=422,
                detail=result.dict()
            )
        
        # Question approved
        logger.info(f"Question {result.question_id} successfully generated and validated")
        return result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in question generation endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Failed to generate question. Please try again later."
        )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
