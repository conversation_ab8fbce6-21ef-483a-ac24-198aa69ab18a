"""Core services for question generation and validation."""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any

from app.models import (
    QuestionGenerationRequest, 
    GeneratedQuestion, 
    QuestionTags,
    ValidationFailureResponse
)
from app.gemini_client import gemini_client
from app.database import chroma_client
from app.config import settings

logger = logging.getLogger(__name__)


class QuestionGenerationService:
    """Service for generating and validating psychometric questions."""
    
    async def generate_and_validate_question(
        self, 
        request: QuestionGenerationRequest
    ) -> GeneratedQuestion | ValidationFailureResponse:
        """
        Complete workflow for generating and validating a psychometric question.
        
        Args:
            request: Question generation request
            
        Returns:
            Either a validated question or validation failure response
        """
        try:
            # Step 1: Generate initial question
            logger.info(f"Generating question for construct: {request.construct}")
            raw_question = await gemini_client.generate_question(
                theory=request.theory,
                construct=request.construct,
                target_age_group=request.target_age_group,
                prompt_context=request.prompt_context
            )
            
            # Step 2: Enrich with metadata
            question_id = f"qid_{uuid.uuid4().hex[:8]}"
            enriched_question = self._enrich_question(
                raw_question=raw_question,
                request=request,
                question_id=question_id
            )
            
            # Step 3: RAG-based validation
            logger.info(f"Validating question {question_id}")
            validation_info = await self._validate_question_with_rag(
                enriched_question=enriched_question,
                construct=request.construct
            )
            
            # Step 4: Decision logic
            if (validation_info.verdict == "PASS" and 
                validation_info.confidence_score > settings.min_confidence_score):
                
                # Question approved
                enriched_question.validation_info = validation_info
                logger.info(f"Question {question_id} approved")
                return enriched_question
            else:
                # Question rejected
                logger.warning(f"Question {question_id} rejected: {validation_info.justification}")
                return ValidationFailureResponse(
                    validation_info=validation_info,
                    question_id=question_id
                )
                
        except Exception as e:
            logger.error(f"Error in question generation workflow: {e}")
            raise
    
    def _enrich_question(
        self, 
        raw_question, 
        request: QuestionGenerationRequest, 
        question_id: str
    ) -> GeneratedQuestion:
        """
        Enrich the raw generated question with metadata.
        
        Args:
            raw_question: Raw question from generation step
            request: Original request
            question_id: Generated question ID
            
        Returns:
            Enriched question with metadata
        """
        tags = QuestionTags(
            theory=request.theory,
            construct=request.construct,
            age_group=request.target_age_group
        )
        
        # Create enriched question (validation_info will be added later)
        enriched_question = GeneratedQuestion(
            question_id=question_id,
            question_text=raw_question.question_text,
            options=raw_question.options,
            tags=tags,
            validation_info=None,  # Will be set after validation
            version=1.0,
            created_at=datetime.utcnow()
        )
        
        return enriched_question
    
    async def _validate_question_with_rag(
        self, 
        enriched_question: GeneratedQuestion, 
        construct: str
    ):
        """
        Validate question using RAG-based approach.
        
        Args:
            enriched_question: Question to validate
            construct: Construct being measured
            
        Returns:
            Validation information
        """
        try:
            # Retrieve relevant knowledge base chunks
            logger.info(f"Retrieving knowledge for construct: {construct}")
            retrieved_chunks = await chroma_client.search_knowledge_base(
                query=construct,
                n_results=settings.max_retrieval_chunks
            )
            
            # Format retrieved chunks for validation prompt
            chunks_text = self._format_retrieved_chunks(retrieved_chunks)
            
            # Convert enriched question to JSON (excluding validation_info)
            question_dict = enriched_question.dict(exclude={"validation_info"})
            enriched_question_json = json.dumps(question_dict, default=str, separators=(',', ':'))
            
            # Call Gemini for validation
            validation_info = await gemini_client.validate_question(
                enriched_question_json=enriched_question_json,
                retrieved_chunks=chunks_text
            )
            
            return validation_info
            
        except Exception as e:
            logger.error(f"Error in RAG validation: {e}")
            raise
    
    def _format_retrieved_chunks(self, chunks: list) -> str:
        """
        Format retrieved chunks for the validation prompt.
        
        Args:
            chunks: List of retrieved document chunks
            
        Returns:
            Formatted chunks text
        """
        if not chunks:
            return "No relevant expert knowledge found in the knowledge base."
        
        formatted_chunks = []
        for i, chunk in enumerate(chunks, 1):
            content = chunk.get("content", "")
            metadata = chunk.get("metadata", {})
            
            chunk_text = f"**Chunk {i}:**\n{content}"
            if metadata:
                chunk_text += f"\n*Source: {metadata}*"
            
            formatted_chunks.append(chunk_text)
        
        return "\n\n".join(formatted_chunks)


# Global service instance
question_service = QuestionGenerationService()
