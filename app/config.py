"""Configuration management for the Psychometric Question Generator API."""

import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Google Gemini API Configuration
    google_api_key: str
    gemini_model: str = "gemini-2.5-flash"
    embedding_model: str = "text-embedding-004"
    
    # ChromaDB Configuration
    chromadb_host: str = "localhost"
    chromadb_port: int = 8000
    chromadb_collection_name: str = "psychometrics_kb"
    
    # FastAPI Configuration
    app_name: str = "Psychometric Question Generator"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Validation Configuration
    min_confidence_score: float = 0.90
    max_retrieval_chunks: int = 3
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
