version: '3.8'

services:
  # ChromaDB service
  chromadb:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chromadb_data:/chroma/chroma
    networks:
      - psychometric_network

  # Psychometric API service
  psychometric-api:
    build: .
    ports:
      - "8001:8000"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - CHROMADB_HOST=chromadb
      - CHROMADB_PORT=8000
      - DEBUG=false
      - LOG_LEVEL=INFO
    depends_on:
      - chromadb
    networks:
      - psychometric_network
    restart: unless-stopped

volumes:
  chromadb_data:

networks:
  psychometric_network:
    driver: bridge
