# Psychometric Question Generator API

A robust Python FastAPI service that serves as the core psychometric question generation pipeline. The service provides a single endpoint that generates complete psychometric questions with weighted scoring options, enriches them with metadata, and validates them against an expert knowledge base using RAG-powered AI verification.

## Features

- **Complete Question Generation**: Creates psychometric questions with multiple-choice options and weighted scoring
- **RAG-Powered Validation**: Uses ChromaDB vector database with expert knowledge for question validation
- **Google Gemini Integration**: Leverages Gemini 2.0 Flash for question generation and validation
- **Robust Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Thread-Safe Operations**: Designed for concurrent requests
- **Comprehensive Logging**: Request/response logging for debugging and monitoring

## Architecture

- **Language/Framework**: Python 3.10+ with FastAPI
- **LLM**: Google Gemini 2.0 Flash (via google-generativeai Python SDK)
- **Vector Database**: ChromaDB (via chromadb Python client)
- **Embedding Model**: Google text-embedding-004 model
- **Database Collection**: ChromaDB collection named 'psychometrics_kb' containing textbook chunks

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd pychometric-cursor
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Configure your environment**:
   - Set `GOOGLE_API_KEY` with your Google AI API key
   - Configure ChromaDB settings if using a remote instance
   - Adjust other settings as needed

## Configuration

The application uses environment variables for configuration. Key settings include:

- `GOOGLE_API_KEY`: Your Google AI API key (required)
- `CHROMADB_HOST`: ChromaDB host (default: localhost)
- `CHROMADB_PORT`: ChromaDB port (default: 8000)
- `CHROMADB_COLLECTION_NAME`: Collection name (default: psychometrics_kb)
- `MIN_CONFIDENCE_SCORE`: Minimum confidence for question approval (default: 0.90)

## Usage

### Starting the Server

```bash
python run.py
```

Or using uvicorn directly:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Endpoint

**POST** `/v1/questions/generate_and_validate`

#### Request Body
```json
{
  "theory": "Big Five",
  "construct": "Agreeableness", 
  "target_age_group": "14-16",
  "prompt_context": "Create a question about collaborating on a difficult school project."
}
```

#### Success Response (200 OK)
```json
{
  "question_id": "qid_abc12345",
  "question_text": "Your team gets negative feedback on a project you all worked hard on. What's your immediate reaction?",
  "options": [
    {
      "option_id": "A",
      "option_text": "Suggest the team meets to understand the feedback without blaming anyone.",
      "scoring_key": {
        "trait": "Agreeableness",
        "weight": 2
      }
    }
  ],
  "tags": {
    "theory": "Big Five",
    "construct": "Agreeableness",
    "age_group": "14-16"
  },
  "validation_info": {
    "verdict": "PASS",
    "confidence_score": 0.98,
    "justification": "The question effectively measures Agreeableness in a relevant context."
  },
  "version": 1.0,
  "created_at": "2025-08-10T10:36:44Z"
}
```

#### Validation Failure Response (422 Unprocessable Entity)
```json
{
  "error": "Validation failed",
  "validation_info": {
    "verdict": "FAIL",
    "confidence_score": 0.75,
    "justification": "Question options do not adequately differentiate trait levels."
  },
  "question_id": "qid_def67890"
}
```

### Health Check

**GET** `/health`

Returns service health status and knowledge base information.

## Workflow

The API endpoint executes these steps in order:

1. **Input Validation**: Validates request body against schema
2. **Question Generation**: Uses Gemini API with Generator Prompt Template
3. **Metadata Enrichment**: Adds tags, question_id, version, and timestamp
4. **RAG-Based Validation**: Retrieves relevant knowledge and validates with Gemini
5. **Decision Logic**: Approves questions with verdict="PASS" and confidence > 0.90

## Development

### Running Tests

```bash
pytest
```

### Code Structure

```
app/
├── __init__.py
├── main.py              # FastAPI application and main endpoint
├── config.py            # Configuration management
├── models.py            # Pydantic models for request/response schemas
├── database.py          # ChromaDB client and operations
├── gemini_client.py     # Google Gemini API client
├── services.py          # Core business logic services
└── exceptions.py        # Custom exception classes
```

## Error Handling

The API provides comprehensive error handling with appropriate HTTP status codes:

- **400 Bad Request**: Invalid request body
- **422 Unprocessable Entity**: Question validation failed
- **500 Internal Server Error**: Server-side errors
- **502 Bad Gateway**: External API errors
- **503 Service Unavailable**: Database connection issues

## Logging

The application provides detailed logging for:
- Request/response cycles
- Question generation steps
- Validation processes
- Error conditions
- Performance metrics

## License

[Add your license information here]
